# 合十消游戏后端 Linux 部署指南

本指南详细说明如何在Linux服务器上部署和运行合十消游戏后端服务。

## 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+)
- **Java**: OpenJDK 11 或 Oracle JDK 11
- **内存**: 最小 1GB，推荐 2GB+
- **磁盘**: 最小 500MB 可用空间
- **网络**: 端口 28888 需要开放

## 快速部署

### 1. 环境准备

#### 安装 Java 11
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk

# CentOS/RHEL
sudo yum install java-11-openjdk-devel

# 验证安装
java -version
```

#### 创建用户和目录
```bash
# 创建专用用户
sudo useradd -r -s /bin/bash gameuser

# 创建部署目录
sudo mkdir -p /opt/heshixiao-game-backend
sudo chown gameuser:gameuser /opt/heshixiao-game-backend
```

### 2. 部署应用

#### 上传文件
```bash
# 将以下文件上传到服务器 /opt/heshixiao-game-backend/
# - target/game-backend-1.0.0.jar
# - start-service.sh
# - heshixiao-game-backend.service
# - src/main/resources/application.yml (可选，用于生产环境配置)

# 设置权限
sudo chown -R gameuser:gameuser /opt/heshixiao-game-backend
sudo chmod +x /opt/heshixiao-game-backend/start-service.sh
```

#### 配置环境变量
```bash
# 编辑 /etc/environment 或 ~/.bashrc
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
export SPRING_PROFILES_ACTIVE=prod
```

### 3. 使用启动脚本

#### 基本命令
```bash
cd /opt/heshixiao-game-backend

# 启动服务
./start-service.sh start

# 查看状态
./start-service.sh status

# 停止服务
./start-service.sh stop

# 重启服务
./start-service.sh restart

# 查看日志
./start-service.sh logs

# 实时查看日志
./start-service.sh tail
```

#### 脚本功能说明

**启动过程**:
- 检查Java环境和JAR文件
- 验证端口可用性
- 创建必要的日志目录
- 后台启动服务并保存PID
- 等待服务启动完成

**监控功能**:
- 进程状态检查
- 端口监听状态
- 日志文件查看
- 资源使用情况

### 4. 配置系统服务 (推荐)

#### 安装 systemd 服务
```bash
# 复制服务文件
sudo cp heshixiao-game-backend.service /etc/systemd/system/

# 修改服务文件中的路径 (如果需要)
sudo nano /etc/systemd/system/heshixiao-game-backend.service

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务 (开机自启)
sudo systemctl enable heshixiao-game-backend

# 启动服务
sudo systemctl start heshixiao-game-backend

# 查看状态
sudo systemctl status heshixiao-game-backend
```

#### systemd 服务管理
```bash
# 启动服务
sudo systemctl start heshixiao-game-backend

# 停止服务
sudo systemctl stop heshixiao-game-backend

# 重启服务
sudo systemctl restart heshixiao-game-backend

# 查看状态
sudo systemctl status heshixiao-game-backend

# 查看日志
sudo journalctl -u heshixiao-game-backend -f

# 禁用开机自启
sudo systemctl disable heshixiao-game-backend
```

## 配置说明

### 应用配置

#### 生产环境配置文件
创建 `application-prod.yml`:
```yaml
server:
  port: 28888
  servlet:
    context-path: /api

spring:
  application:
    name: heshixiao-game-backend
  
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:}
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000

logging:
  level:
    com.heshixiao: info
    root: warn
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30
```

#### 环境变量配置
```bash
# 数据库配置
export DB_USERNAME=gameuser
export DB_PASSWORD=your_secure_password

# JVM 配置
export JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC"

# Spring 配置
export SPRING_PROFILES_ACTIVE=prod
```

### 网络配置

#### 防火墙设置
```bash
# Ubuntu (ufw)
sudo ufw allow 28888/tcp

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=28888/tcp
sudo firewall-cmd --reload

# iptables
sudo iptables -A INPUT -p tcp --dport 28888 -j ACCEPT
```

#### Nginx 反向代理 (可选)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:28888/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和维护

### 日志管理
```bash
# 查看应用日志
tail -f /opt/heshixiao-game-backend/logs/application.log

# 查看系统日志
sudo journalctl -u heshixiao-game-backend -f

# 日志轮转配置 /etc/logrotate.d/heshixiao-game-backend
/opt/heshixiao-game-backend/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
```

### 健康检查
```bash
# 检查服务状态
curl -f http://localhost:28888/api/health || echo "Service is down"

# 检查进程
ps aux | grep game-backend

# 检查端口
netstat -tuln | grep 28888
```

### 性能监控
```bash
# 内存使用
ps -p $(cat /opt/heshixiao-game-backend/heshixiao-game-backend.pid) -o pid,vsz,rss,pmem

# CPU 使用
top -p $(cat /opt/heshixiao-game-backend/heshixiao-game-backend.pid)
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用进程
   sudo lsof -i :28888
   # 或
   sudo netstat -tulpn | grep 28888
   ```

2. **Java 版本不兼容**
   ```bash
   # 检查 Java 版本
   java -version
   # 设置正确的 JAVA_HOME
   export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la /opt/heshixiao-game-backend/
   # 修复权限
   sudo chown -R gameuser:gameuser /opt/heshixiao-game-backend
   ```

4. **内存不足**
   ```bash
   # 调整 JVM 参数
   export JAVA_OPTS="-Xms256m -Xmx512m"
   ```

### 日志分析
```bash
# 查看启动错误
grep -i error /opt/heshixiao-game-backend/logs/application.log

# 查看数据库连接问题
grep -i "connection" /opt/heshixiao-game-backend/logs/application.log

# 查看内存问题
grep -i "outofmemory" /opt/heshixiao-game-backend/logs/application.log
```

## 安全建议

1. **用户权限**: 使用专用的非特权用户运行服务
2. **文件权限**: 限制配置文件的读取权限
3. **网络安全**: 配置防火墙，只开放必要端口
4. **数据库安全**: 使用强密码，限制数据库访问
5. **日志安全**: 定期清理和轮转日志文件

## 备份和恢复

### 备份脚本
```bash
#!/bin/bash
BACKUP_DIR="/backup/heshixiao-game-backend"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    /opt/heshixiao-game-backend \
    --exclude=/opt/heshixiao-game-backend/logs
```

### 恢复步骤
```bash
# 停止服务
sudo systemctl stop heshixiao-game-backend

# 恢复文件
tar -xzf app_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# 启动服务
sudo systemctl start heshixiao-game-backend
```

通过以上配置，您的合十消游戏后端服务将能够稳定运行在Linux服务器上。
