package com.heshixiao.game.controller;

import com.heshixiao.game.common.Result;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 静态资源控制器
 * 提供音频、图片等静态文件的访问接口
 */
@RestController
@RequestMapping("/static")
@CrossOrigin(originPatterns = "*")
public class StaticResourceController {

    /**
     * 获取音频文件
     * @param filename 音频文件名
     * @return 音频文件流
     */
    @GetMapping("/audio/{filename}")
    public ResponseEntity<Resource> getAudioFile(@PathVariable String filename) {
        try {
            // 构建文件路径
            String filePath = "static/audio/" + filename;
            Resource resource = new ClassPathResource(filePath);
            
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            // 根据文件扩展名设置Content-Type
            String contentType = getAudioContentType(filename);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .body(resource);
                    
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取图片文件
     * @param filename 图片文件名
     * @return 图片文件流
     */
    @GetMapping("/images/{filename}")
    public ResponseEntity<Resource> getImageFile(@PathVariable String filename) {
        try {
            // 构建文件路径
            String filePath = "static/images/" + filename;
            Resource resource = new ClassPathResource(filePath);
            
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            // 根据文件扩展名设置Content-Type
            String contentType = getImageContentType(filename);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                    .body(resource);
                    
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取可用的音频文件列表
     * @return 音频文件列表
     */
    @GetMapping("/audio/list")
    public Result<Map<String, String>> getAudioList() {
        try {
            Map<String, String> audioFiles = new HashMap<>();
            audioFiles.put("home", "/api/static/audio/主界面背景音乐.mp3");
            audioFiles.put("game", "/api/static/audio/欢乐游戏背景音效.mp3");
            audioFiles.put("eliminate", "/api/static/audio/消除音效.mp3");
            
            return Result.success(audioFiles);
        } catch (Exception e) {
            return Result.error("获取音频文件列表失败");
        }
    }

    /**
     * 获取可用的图片文件列表
     * @return 图片文件列表
     */
    @GetMapping("/images/list")
    public Result<Map<String, String>> getImageList() {
        try {
            Map<String, String> imageFiles = new HashMap<>();
            imageFiles.put("logo", "/api/static/images/logo.png");
            imageFiles.put("default-avatar", "/api/static/images/default-avatar.png");
            imageFiles.put("share-image", "/api/static/images/share-image.png");
            
            return Result.success(imageFiles);
        } catch (Exception e) {
            return Result.error("获取图片文件列表失败");
        }
    }

    /**
     * 根据文件名获取音频Content-Type
     */
    private String getAudioContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "mp3":
                return "audio/mpeg";
            case "wav":
                return "audio/wav";
            case "ogg":
                return "audio/ogg";
            case "m4a":
                return "audio/mp4";
            default:
                return "audio/mpeg";
        }
    }

    /**
     * 根据文件名获取图片Content-Type
     */
    private String getImageContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "png":
                return "image/png";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "gif":
                return "image/gif";
            case "webp":
                return "image/webp";
            default:
                return "image/png";
        }
    }
}
