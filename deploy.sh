#!/bin/bash

# 合十消游戏后端一键部署脚本
# 适用于 Ubuntu/Debian 和 CentOS/RHEL 系统

set -e

# 配置变量
PROJECT_NAME="heshixiao-game-backend"
DEPLOY_DIR="/opt/${PROJECT_NAME}"
SERVICE_USER="gameuser"
JAVA_VERSION="11"
SERVICE_PORT="28888"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "Please run this script as root (use sudo)"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "Cannot detect operating system"
        exit 1
    fi
    
    log_info "Detected OS: $OS $VER"
}

# 安装Java
install_java() {
    log_step "Installing Java $JAVA_VERSION..."
    
    case "$OS" in
        *"Ubuntu"*|*"Debian"*)
            apt update
            apt install -y openjdk-${JAVA_VERSION}-jdk
            ;;
        *"CentOS"*|*"Red Hat"*|*"Rocky"*|*"AlmaLinux"*)
            if command -v dnf >/dev/null 2>&1; then
                dnf install -y java-${JAVA_VERSION}-openjdk-devel
            else
                yum install -y java-${JAVA_VERSION}-openjdk-devel
            fi
            ;;
        *)
            log_error "Unsupported operating system: $OS"
            exit 1
            ;;
    esac
    
    # 设置JAVA_HOME
    JAVA_HOME=$(readlink -f /usr/bin/java | sed "s:bin/java::")
    echo "export JAVA_HOME=$JAVA_HOME" >> /etc/environment
    
    log_info "Java installed successfully"
    java -version
}

# 创建用户
create_user() {
    log_step "Creating service user: $SERVICE_USER"
    
    if id "$SERVICE_USER" &>/dev/null; then
        log_warn "User $SERVICE_USER already exists"
    else
        useradd -r -s /bin/bash -d "$DEPLOY_DIR" "$SERVICE_USER"
        log_info "User $SERVICE_USER created"
    fi
}

# 创建目录结构
create_directories() {
    log_step "Creating directory structure..."
    
    mkdir -p "$DEPLOY_DIR"
    mkdir -p "$DEPLOY_DIR/logs"
    mkdir -p "$DEPLOY_DIR/config"
    mkdir -p "$DEPLOY_DIR/backup"
    
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_DIR"
    log_info "Directories created: $DEPLOY_DIR"
}

# 配置防火墙
configure_firewall() {
    log_step "Configuring firewall for port $SERVICE_PORT..."
    
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu/Debian with ufw
        ufw allow "$SERVICE_PORT/tcp"
        log_info "UFW rule added for port $SERVICE_PORT"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL with firewalld
        firewall-cmd --permanent --add-port="$SERVICE_PORT/tcp"
        firewall-cmd --reload
        log_info "Firewalld rule added for port $SERVICE_PORT"
    elif command -v iptables >/dev/null 2>&1; then
        # Fallback to iptables
        iptables -A INPUT -p tcp --dport "$SERVICE_PORT" -j ACCEPT
        # Save iptables rules
        if command -v iptables-save >/dev/null 2>&1; then
            iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
        fi
        log_info "Iptables rule added for port $SERVICE_PORT"
    else
        log_warn "No firewall management tool found. Please manually open port $SERVICE_PORT"
    fi
}

# 安装应用文件
install_application() {
    log_step "Installing application files..."
    
    # 检查JAR文件
    if [ ! -f "target/game-backend-1.0.0.jar" ]; then
        log_error "JAR file not found: target/game-backend-1.0.0.jar"
        log_info "Please build the project first: mvn clean package"
        exit 1
    fi
    
    # 复制文件
    cp target/game-backend-1.0.0.jar "$DEPLOY_DIR/"
    cp start-service.sh "$DEPLOY_DIR/"
    cp heshixiao-game-backend.service /etc/systemd/system/
    
    # 复制配置文件
    if [ -f "src/main/resources/application.yml" ]; then
        cp src/main/resources/application.yml "$DEPLOY_DIR/config/"
    fi
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DEPLOY_DIR"
    chmod +x "$DEPLOY_DIR/start-service.sh"
    
    # 更新systemd服务文件中的路径
    sed -i "s|/opt/heshixiao-game-backend|$DEPLOY_DIR|g" /etc/systemd/system/heshixiao-game-backend.service
    
    log_info "Application files installed"
}

# 配置systemd服务
configure_service() {
    log_step "Configuring systemd service..."
    
    systemctl daemon-reload
    systemctl enable heshixiao-game-backend
    
    log_info "Service configured and enabled"
}

# 创建管理脚本
create_management_script() {
    log_step "Creating management script..."
    
    cat > /usr/local/bin/heshixiao-game << 'EOF'
#!/bin/bash
# 合十消游戏后端管理脚本

DEPLOY_DIR="/opt/heshixiao-game-backend"

case "$1" in
    start)
        echo "Starting heshixiao-game-backend service..."
        systemctl start heshixiao-game-backend
        ;;
    stop)
        echo "Stopping heshixiao-game-backend service..."
        systemctl stop heshixiao-game-backend
        ;;
    restart)
        echo "Restarting heshixiao-game-backend service..."
        systemctl restart heshixiao-game-backend
        ;;
    status)
        systemctl status heshixiao-game-backend
        ;;
    logs)
        journalctl -u heshixiao-game-backend -n 50
        ;;
    tail)
        journalctl -u heshixiao-game-backend -f
        ;;
    script)
        shift
        sudo -u gameuser "$DEPLOY_DIR/start-service.sh" "$@"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|tail|script}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the service"
        echo "  stop     - Stop the service"
        echo "  restart  - Restart the service"
        echo "  status   - Show service status"
        echo "  logs     - Show recent logs"
        echo "  tail     - Follow logs in real-time"
        echo "  script   - Run start-service.sh commands"
        exit 1
        ;;
esac
EOF
    
    chmod +x /usr/local/bin/heshixiao-game
    log_info "Management script created: /usr/local/bin/heshixiao-game"
}

# 启动服务
start_service() {
    log_step "Starting service..."
    
    systemctl start heshixiao-game-backend
    
    # 等待服务启动
    sleep 5
    
    if systemctl is-active --quiet heshixiao-game-backend; then
        log_info "Service started successfully!"
    else
        log_error "Service failed to start. Check logs with: journalctl -u heshixiao-game-backend"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=================================="
    echo "  部署完成！"
    echo "=================================="
    echo ""
    echo "服务信息:"
    echo "  名称: $PROJECT_NAME"
    echo "  端口: $SERVICE_PORT"
    echo "  目录: $DEPLOY_DIR"
    echo "  用户: $SERVICE_USER"
    echo ""
    echo "管理命令:"
    echo "  heshixiao-game start    # 启动服务"
    echo "  heshixiao-game stop     # 停止服务"
    echo "  heshixiao-game restart  # 重启服务"
    echo "  heshixiao-game status   # 查看状态"
    echo "  heshixiao-game logs     # 查看日志"
    echo "  heshixiao-game tail     # 实时日志"
    echo ""
    echo "服务状态:"
    systemctl status heshixiao-game-backend --no-pager -l
    echo ""
    echo "访问地址: http://$(hostname -I | awk '{print $1}'):$SERVICE_PORT/api"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "  合十消游戏后端自动部署脚本"
    echo "========================================"
    echo ""
    
    check_root
    detect_os
    
    log_info "Starting deployment process..."
    
    install_java
    create_user
    create_directories
    configure_firewall
    install_application
    configure_service
    create_management_script
    start_service
    
    show_deployment_info
    
    log_info "Deployment completed successfully!"
}

# 执行主函数
main "$@"
