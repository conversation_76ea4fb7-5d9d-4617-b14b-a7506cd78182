[Unit]
Description=合十消游戏后端服务
Documentation=https://github.com/your-repo/heshixiao-game-backend
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=forking
User=gameuser
Group=gameuser

# 工作目录 - 请根据实际部署路径修改
WorkingDirectory=/opt/heshixiao-game-backend

# Java环境配置
Environment="JAVA_HOME=/usr/lib/jvm/java-11-openjdk"
Environment="SPRING_PROFILES_ACTIVE=prod"

# JVM参数
Environment="JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication -Djava.security.egd=file:/dev/./urandom"

# 启动命令
ExecStart=/opt/heshixiao-game-backend/start-service.sh start
ExecStop=/opt/heshixiao-game-backend/start-service.sh stop
ExecReload=/opt/heshixiao-game-backend/start-service.sh restart

# PID文件
PIDFile=/opt/heshixiao-game-backend/heshixiao-game-backend.pid

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/heshixiao-game-backend/logs
ReadWritePaths=/opt/heshixiao-game-backend/heshixiao-game-backend.pid

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 超时配置
TimeoutStartSec=120
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
