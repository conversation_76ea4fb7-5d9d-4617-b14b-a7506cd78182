#!/bin/bash

# 合十消游戏后端服务启动脚本
# 项目名称: heshixiao-game-backend
# 版本: 1.0.0

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="heshixiao-game-backend"
JAR_NAME="game-backend-1.0.0.jar"
SERVICE_PORT=28888

# 路径配置
PROJECT_DIR="${SCRIPT_DIR}"
JAR_PATH="${PROJECT_DIR}/target/${JAR_NAME}"
PID_FILE="${PROJECT_DIR}/${PROJECT_NAME}.pid"
LOG_FILE="${PROJECT_DIR}/logs/application.log"
LOG_DIR="${PROJECT_DIR}/logs"

# Java配置
JAVA_HOME="${JAVA_HOME:-/usr/lib/jvm/java-11-openjdk}"
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication"
SPRING_PROFILES_ACTIVE="${SPRING_PROFILES_ACTIVE:-prod}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Java环境
check_java() {
    if [ ! -d "$JAVA_HOME" ]; then
        log_error "JAVA_HOME not found: $JAVA_HOME"
        log_info "Please set JAVA_HOME environment variable or install Java 11"
        exit 1
    fi
    
    JAVA_CMD="$JAVA_HOME/bin/java"
    if [ ! -x "$JAVA_CMD" ]; then
        log_error "Java executable not found: $JAVA_CMD"
        exit 1
    fi
    
    JAVA_VERSION=$($JAVA_CMD -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Using Java: $JAVA_VERSION at $JAVA_HOME"
}

# 检查JAR文件
check_jar() {
    if [ ! -f "$JAR_PATH" ]; then
        log_error "JAR file not found: $JAR_PATH"
        log_info "Please build the project first: mvn clean package"
        exit 1
    fi
    log_info "JAR file found: $JAR_PATH"
}

# 检查端口占用
check_port() {
    if netstat -tuln | grep -q ":$SERVICE_PORT "; then
        log_warn "Port $SERVICE_PORT is already in use"
        local pid=$(lsof -ti:$SERVICE_PORT)
        if [ -n "$pid" ]; then
            log_warn "Process using port $SERVICE_PORT: PID $pid"
        fi
        return 1
    fi
    return 0
}

# 创建必要目录
create_directories() {
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        log_info "Created log directory: $LOG_DIR"
    fi
}

# 获取进程PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    fi
}

# 检查服务状态
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        return 0
    fi
    return 1
}

# 启动服务
start_service() {
    log_info "Starting $PROJECT_NAME service..."
    
    # 检查是否已经运行
    if is_running; then
        log_warn "Service is already running with PID $(get_pid)"
        return 1
    fi
    
    # 环境检查
    check_java
    check_jar
    create_directories
    
    # 检查端口
    if ! check_port; then
        log_error "Cannot start service: port $SERVICE_PORT is in use"
        return 1
    fi
    
    # 构建启动命令
    local cmd="$JAVA_CMD $JAVA_OPTS"
    cmd="$cmd -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE"
    cmd="$cmd -Dlogging.file.name=$LOG_FILE"
    cmd="$cmd -Dserver.port=$SERVICE_PORT"
    cmd="$cmd -jar $JAR_PATH"
    
    log_info "Starting command: $cmd"
    
    # 启动服务
    nohup $cmd > "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待启动
    log_info "Waiting for service to start..."
    local count=0
    while [ $count -lt 30 ]; do
        if is_running && check_port_listening; then
            log_info "Service started successfully with PID $pid"
            log_info "Service is listening on port $SERVICE_PORT"
            log_info "Log file: $LOG_FILE"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done
    
    echo
    log_error "Service failed to start within 60 seconds"
    log_info "Check log file for details: $LOG_FILE"
    return 1
}

# 检查端口监听状态
check_port_listening() {
    netstat -tuln | grep -q ":$SERVICE_PORT "
}

# 停止服务
stop_service() {
    log_info "Stopping $PROJECT_NAME service..."
    
    local pid=$(get_pid)
    if [ -z "$pid" ]; then
        log_warn "PID file not found, service may not be running"
        return 1
    fi
    
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warn "Process with PID $pid is not running"
        rm -f "$PID_FILE"
        return 1
    fi
    
    # 优雅停止
    log_info "Sending TERM signal to PID $pid"
    kill -TERM "$pid"
    
    # 等待进程结束
    local count=0
    while [ $count -lt 15 ]; do
        if ! kill -0 "$pid" 2>/dev/null; then
            log_info "Service stopped successfully"
            rm -f "$PID_FILE"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done
    
    # 强制停止
    echo
    log_warn "Service did not stop gracefully, forcing shutdown"
    kill -KILL "$pid" 2>/dev/null
    rm -f "$PID_FILE"
    log_info "Service force stopped"
}

# 重启服务
restart_service() {
    log_info "Restarting $PROJECT_NAME service..."
    stop_service
    sleep 3
    start_service
}

# 查看服务状态
status_service() {
    echo "=== $PROJECT_NAME Service Status ==="
    
    if is_running; then
        local pid=$(get_pid)
        echo -e "Status: ${GREEN}RUNNING${NC}"
        echo "PID: $pid"
        echo "Port: $SERVICE_PORT"
        echo "Log: $LOG_FILE"
        
        # 显示进程信息
        if command -v ps >/dev/null 2>&1; then
            echo "Process Info:"
            ps -p "$pid" -o pid,ppid,user,start,time,command 2>/dev/null || echo "  Process details not available"
        fi
        
        # 显示端口监听状态
        if check_port_listening; then
            echo -e "Port Status: ${GREEN}LISTENING${NC}"
        else
            echo -e "Port Status: ${RED}NOT LISTENING${NC}"
        fi
    else
        echo -e "Status: ${RED}STOPPED${NC}"
        if [ -f "$PID_FILE" ]; then
            echo "Stale PID file found: $PID_FILE"
        fi
    fi
    
    echo "JAR: $JAR_PATH"
    echo "Java: $JAVA_HOME"
    echo "Profile: $SPRING_PROFILES_ACTIVE"
}

# 查看日志
view_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "Showing last 50 lines of $LOG_FILE"
        echo "=================================="
        tail -n 50 "$LOG_FILE"
    else
        log_warn "Log file not found: $LOG_FILE"
    fi
}

# 实时查看日志
tail_logs() {
    if [ -f "$LOG_FILE" ]; then
        log_info "Following log file: $LOG_FILE (Press Ctrl+C to exit)"
        tail -f "$LOG_FILE"
    else
        log_warn "Log file not found: $LOG_FILE"
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 {start|stop|restart|status|logs|tail|help}"
    echo ""
    echo "Commands:"
    echo "  start    - Start the service"
    echo "  stop     - Stop the service"
    echo "  restart  - Restart the service"
    echo "  status   - Show service status"
    echo "  logs     - Show recent logs"
    echo "  tail     - Follow logs in real-time"
    echo "  help     - Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  JAVA_HOME              - Java installation directory"
    echo "  SPRING_PROFILES_ACTIVE - Spring profile (default: prod)"
    echo ""
    echo "Configuration:"
    echo "  Project: $PROJECT_NAME"
    echo "  JAR: $JAR_PATH"
    echo "  Port: $SERVICE_PORT"
    echo "  Logs: $LOG_FILE"
}

# 主函数
main() {
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            status_service
            ;;
        logs)
            view_logs
            ;;
        tail)
            tail_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "Invalid command: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
